import classNames from 'classnames'
import PropTypes from 'prop-types'
import { memo, useRef } from 'react'
import { useSelector } from 'react-redux'

import { getConversationSourceType } from 'apps/mailbox/state/conversation/reducer'

import {
  ConversationEditorUI,
  InlineComposerControlsUI,
  SaveButtonUI,
} from './InlineConversationEditor.css'
import {
  ComposerContainerUI,
  ComposerUI,
} from 'apps/mailbox/screens/ConversationView/components/Content/components/ReplyBar/components/Composer/Composer.css'

import { ComposerFloatingControls } from '../../ReplyBar/components/FloatingControls/ComposerFloatingControls'
import { useInlineConversationEditor } from './useInlineConversationEditor'

function InlineConversationEditor(props) {
  const { isNote, onCancel, onSave, value, isWideLayout, ...rest } = props

  const sourceType = useSelector(getConversationSourceType)
  const {
    isUploadingAttachments,
    isDirty,
    editorConfig,
    handleOnCancel,
    handleOnChange,
    handleEditorSave,
    handleSubmitKeyboard,
    handleSubmitButton,
    actionsToolbarSelector,
  } = useInlineConversationEditor(onCancel, onSave, { isNote })

  const containerRef = useRef()

  return (
    <ComposerUI
      className={classNames(
        isNote && 'is-note',
        'is-inline-composer',
        isWideLayout && 'is-wide-layout'
      )}
    >
      <ComposerContainerUI ref={containerRef}>
        <ComposerFloatingControls
          enableSave={false}
          handleCancel={handleOnCancel}
          cancelType="close"
        />
        <ConversationEditorUI
          // eslint-disable-next-line jsx-a11y/no-autofocus
          autoFocus={true}
          autoFocusDelay={1}
          config={editorConfig}
          onChange={handleOnChange}
          onSave={handleEditorSave}
          onSubmit={handleSubmitKeyboard}
          onCancelKeydown={handleOnCancel}
          data-testid={`${isNote ? 'note' : 'reply'}-inline-editor`}
          aria-label={`${isNote ? 'Note' : 'Reply'} Editor`}
          initialValue={value}
          isShowingDropZone={false}
          setIsShowingDropZone={() => null}
          isNote={isNote}
          {...rest}
          source={sourceType}
          showSignature={false}
        />
        <InlineComposerControlsUI>
          <div id={actionsToolbarSelector}></div>
          <SaveButtonUI
            outlined={true}
            color={isNote ? 'yellow' : 'blue'}
            className={isNote ? 'is-note' : ''}
            size="lg"
            onClick={handleSubmitButton}
            disabled={!isDirty || isUploadingAttachments}
          >
            Save
          </SaveButtonUI>
        </InlineComposerControlsUI>
      </ComposerContainerUI>
    </ComposerUI>
  )
}

InlineConversationEditor.propTypes = {
  attachments: PropTypes.array,
  isNote: PropTypes.bool,
  onCancel: PropTypes.func.isRequired,
  onSave: PropTypes.func.isRequired,
  value: PropTypes.any.isRequired,
  threadId: PropTypes.number,
  isWideLayout: PropTypes.bool,
}

export default memo(InlineConversationEditor)
